<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1755461441544" clover="3.2.0">
  <project timestamp="1755461441544" name="All files">
    <metrics statements="214" coveredstatements="187" conditionals="103" coveredconditionals="79" methods="35" coveredmethods="31" elements="352" coveredelements="297" complexity="0" loc="214" ncloc="214" packages="1" files="1" classes="1"/>
    <file name="localtail.js" path="/Users/<USER>/repos/personal/exp_web_localtail/implementation/src/localtail.js">
      <metrics statements="214" coveredstatements="187" conditionals="103" coveredconditionals="79" methods="35" coveredmethods="31"/>
      <line num="17" count="18" type="stmt"/>
      <line num="18" count="18" type="stmt"/>
      <line num="19" count="18" type="stmt"/>
      <line num="28" count="18" type="stmt"/>
      <line num="29" count="18" type="stmt"/>
      <line num="30" count="18" type="stmt"/>
      <line num="31" count="18" type="stmt"/>
      <line num="32" count="18" type="stmt"/>
      <line num="33" count="18" type="stmt"/>
      <line num="38" count="18" type="cond" truecount="5" falsecount="0"/>
      <line num="39" count="3" type="stmt"/>
      <line num="42" count="15" type="stmt"/>
      <line num="44" count="15" type="stmt"/>
      <line num="45" count="14" type="stmt"/>
      <line num="46" count="14" type="stmt"/>
      <line num="49" count="14" type="stmt"/>
      <line num="52" count="14" type="stmt"/>
      <line num="54" count="14" type="stmt"/>
      <line num="56" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="57" count="1" type="stmt"/>
      <line num="59" count="0" type="stmt"/>
      <line num="64" count="14" type="stmt"/>
      <line num="65" count="14" type="stmt"/>
      <line num="68" count="14" type="stmt"/>
      <line num="70" count="14" type="cond" truecount="2" falsecount="0"/>
      <line num="71" count="1" type="stmt"/>
      <line num="72" count="1" type="stmt"/>
      <line num="73" count="1" type="stmt"/>
      <line num="76" count="13" type="stmt"/>
      <line num="79" count="13" type="cond" truecount="2" falsecount="0"/>
      <line num="80" count="1" type="stmt"/>
      <line num="81" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="82" count="0" type="stmt"/>
      <line num="84" count="1" type="stmt"/>
      <line num="89" count="13" type="cond" truecount="4" falsecount="0"/>
      <line num="90" count="1" type="stmt"/>
      <line num="92" count="20971" type="stmt"/>
      <line num="93" count="1" type="stmt"/>
      <line num="94" count="1" type="cond" truecount="3" falsecount="1"/>
      <line num="95" count="1" type="stmt"/>
      <line num="99" count="13" type="stmt"/>
      <line num="100" count="13" type="stmt"/>
      <line num="102" count="0" type="stmt"/>
      <line num="108" count="14" type="stmt"/>
      <line num="109" count="3367" type="cond" truecount="2" falsecount="0"/>
      <line num="110" count="1" type="stmt"/>
      <line num="113" count="13" type="stmt"/>
      <line num="117" count="14" type="stmt"/>
      <line num="123" count="14" type="stmt"/>
      <line num="124" count="8" type="stmt"/>
      <line num="125" count="8" type="stmt"/>
      <line num="128" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="129" count="0" type="stmt"/>
      <line num="134" count="14" type="stmt"/>
      <line num="136" count="2" type="cond" truecount="1" falsecount="1"/>
      <line num="137" count="0" type="stmt"/>
      <line num="143" count="8" type="stmt"/>
      <line num="144" count="8" type="stmt"/>
      <line num="146" count="8" type="cond" truecount="1" falsecount="1"/>
      <line num="148" count="8" type="stmt"/>
      <line num="149" count="8" type="stmt"/>
      <line num="150" count="8" type="stmt"/>
      <line num="151" count="8" type="stmt"/>
      <line num="152" count="8" type="stmt"/>
      <line num="154" count="8" type="stmt"/>
      <line num="161" count="8" type="cond" truecount="2" falsecount="0"/>
      <line num="162" count="7" type="stmt"/>
      <line num="165" count="8" type="stmt"/>
      <line num="169" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="170" count="0" type="stmt"/>
      <line num="176" count="8" type="stmt"/>
      <line num="177" count="8" type="stmt"/>
      <line num="183" count="8" type="stmt"/>
      <line num="184" count="8" type="stmt"/>
      <line num="185" count="8" type="stmt"/>
      <line num="188" count="8" type="stmt"/>
      <line num="189" count="8" type="stmt"/>
      <line num="192" count="8" type="stmt"/>
      <line num="197" count="1" type="stmt"/>
      <line num="199" count="1" type="stmt"/>
      <line num="201" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="202" count="0" type="stmt"/>
      <line num="203" count="0" type="stmt"/>
      <line num="204" count="0" type="stmt"/>
      <line num="205" count="0" type="stmt"/>
      <line num="206" count="0" type="stmt"/>
      <line num="208" count="0" type="stmt"/>
      <line num="216" count="0" type="stmt"/>
      <line num="223" count="1" type="stmt"/>
      <line num="230" count="0" type="stmt"/>
      <line num="234" count="5" type="cond" truecount="1" falsecount="1"/>
      <line num="235" count="5" type="stmt"/>
      <line num="247" count="18" type="stmt"/>
      <line num="248" count="18" type="stmt"/>
      <line num="250" count="14" type="stmt"/>
      <line num="260" count="1" type="stmt"/>
      <line num="261" count="0" type="stmt"/>
      <line num="262" count="5" type="stmt"/>
      <line num="263" count="0" type="stmt"/>
      <line num="264" count="0" type="stmt"/>
      <line num="273" count="24" type="stmt"/>
      <line num="274" count="24" type="stmt"/>
      <line num="279" count="24" type="stmt"/>
      <line num="280" count="24" type="stmt"/>
      <line num="287" count="24" type="cond" truecount="4" falsecount="0"/>
      <line num="288" count="2" type="stmt"/>
      <line num="291" count="22" type="stmt"/>
      <line num="292" count="22" type="stmt"/>
      <line num="295" count="22" type="stmt"/>
      <line num="296" count="22" type="cond" truecount="2" falsecount="0"/>
      <line num="297" count="15" type="stmt"/>
      <line num="298" count="15" type="stmt"/>
      <line num="299" count="15" type="stmt"/>
      <line num="306" count="15" type="stmt"/>
      <line num="310" count="7" type="stmt"/>
      <line num="311" count="7" type="cond" truecount="2" falsecount="0"/>
      <line num="312" count="5" type="stmt"/>
      <line num="313" count="5" type="stmt"/>
      <line num="314" count="5" type="stmt"/>
      <line num="321" count="5" type="stmt"/>
      <line num="322" count="5" type="stmt"/>
      <line num="330" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="331" count="1" type="stmt"/>
      <line num="334" count="1" type="stmt"/>
      <line num="341" count="18" type="stmt"/>
      <line num="342" count="18" type="stmt"/>
      <line num="344" count="18" type="stmt"/>
      <line num="345" count="180" type="cond" truecount="2" falsecount="0"/>
      <line num="346" count="150" type="stmt"/>
      <line num="350" count="18" type="stmt"/>
      <line num="357" count="58220" type="stmt"/>
      <line num="358" count="58220" type="cond" truecount="1" falsecount="1"/>
      <line num="359" count="58220" type="stmt"/>
      <line num="361" count="0" type="stmt"/>
      <line num="369" count="20" type="stmt"/>
      <line num="370" count="17519" type="cond" truecount="2" falsecount="0"/>
      <line num="371" count="1" type="stmt"/>
      <line num="374" count="19" type="stmt"/>
      <line num="381" count="18" type="stmt"/>
      <line num="382" count="18" type="stmt"/>
      <line num="384" count="18" type="stmt"/>
      <line num="385" count="37169" type="stmt"/>
      <line num="386" count="37169" type="stmt"/>
      <line num="388" count="37169" type="cond" truecount="1" falsecount="1"/>
      <line num="389" count="37169" type="stmt"/>
      <line num="392" count="37169" type="stmt"/>
      <line num="395" count="18" type="stmt"/>
      <line num="402" count="18" type="cond" truecount="1" falsecount="1"/>
      <line num="403" count="0" type="stmt"/>
      <line num="406" count="18" type="stmt"/>
      <line num="407" count="18" type="stmt"/>
      <line num="409" count="18" type="stmt"/>
      <line num="410" count="37169" type="stmt"/>
      <line num="411" count="37169" type="cond" truecount="3" falsecount="1"/>
      <line num="412" count="0" type="stmt"/>
      <line num="413" count="0" type="stmt"/>
      <line num="417" count="18" type="stmt"/>
      <line num="424" count="3" type="stmt"/>
      <line num="425" count="3" type="stmt"/>
      <line num="426" count="3" type="stmt"/>
      <line num="427" count="3" type="stmt"/>
      <line num="429" count="3" type="stmt"/>
      <line num="430" count="210" type="cond" truecount="1" falsecount="1"/>
      <line num="432" count="210" type="stmt"/>
      <line num="433" count="210" type="stmt"/>
      <line num="434" count="210" type="stmt"/>
      <line num="437" count="3" type="stmt"/>
      <line num="450" count="25" type="cond" truecount="4" falsecount="1"/>
      <line num="451" count="0" type="stmt"/>
      <line num="454" count="25" type="stmt"/>
      <line num="456" count="25" type="stmt"/>
      <line num="459" count="24" type="stmt"/>
      <line num="462" count="24" type="stmt"/>
      <line num="463" count="20" type="cond" truecount="3" falsecount="0"/>
      <line num="464" count="20" type="cond" truecount="2" falsecount="0"/>
      <line num="467" count="20" type="stmt"/>
      <line num="470" count="20" type="stmt"/>
      <line num="471" count="20" type="cond" truecount="2" falsecount="0"/>
      <line num="472" count="1" type="stmt"/>
      <line num="475" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="478" count="19" type="cond" truecount="2" falsecount="0"/>
      <line num="479" count="1" type="stmt"/>
      <line num="488" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="489" count="0" type="stmt"/>
      <line num="492" count="1" type="stmt"/>
      <line num="496" count="18" type="stmt"/>
      <line num="497" count="18" type="stmt"/>
      <line num="498" count="18" type="stmt"/>
      <line num="500" count="18" type="cond" truecount="2" falsecount="0"/>
      <line num="502" count="3" type="stmt"/>
      <line num="503" count="3" type="stmt"/>
      <line num="507" count="18" type="stmt"/>
      <line num="510" count="18" type="stmt"/>
      <line num="513" count="18" type="stmt"/>
      <line num="516" count="18" type="cond" truecount="2" falsecount="0"/>
      <line num="517" count="5" type="stmt"/>
      <line num="518" count="5" type="stmt"/>
      <line num="519" count="21051" type="stmt"/>
      <line num="520" count="21051" type="cond" truecount="1" falsecount="1"/>
      <line num="521" count="21051" type="cond" truecount="2" falsecount="0"/>
      <line num="523" count="0" type="stmt"/>
      <line num="525" count="5" type="stmt"/>
      <line num="529" count="18" type="stmt"/>
      <line num="530" count="18" type="cond" truecount="3" falsecount="1"/>
      <line num="531" count="18" type="stmt"/>
      <line num="532" count="18" type="cond" truecount="1" falsecount="1"/>
      <line num="533" count="18" type="stmt"/>
      <line num="537" count="18" type="stmt"/>
      <line num="548" count="18" type="cond" truecount="2" falsecount="0"/>
      <line num="549" count="5" type="stmt"/>
      <line num="552" count="18" type="stmt"/>
      <line num="555" count="6" type="cond" truecount="2" falsecount="0"/>
      <line num="556" count="1" type="stmt"/>
      <line num="558" count="5" type="stmt"/>
    </file>
  </project>
</coverage>
