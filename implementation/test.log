Starting test application
Loading test configuration
Test configuration loaded
Initializing test database
Test database ready
Starting test server
Test server running on port 8080
Processing test request 1
Processing test request 2
Processing test request 3
Test application ready
File uploaded #1 (WARN)
Cache updated #2 (ERROR)
Disk space monitored #3 (ERROR)
Processing user request #4 (DEBUG)
Cache updated #5 (WARN)
File uploaded #6 (DEBUG)
File uploaded #7 (DEBUG)
Disk space monitored #8 (ERROR)
Email notification sent #9 (INFO)
Background job completed #10 (ERROR)
Processing user request #11 (INFO)
Database query executed #12 (DEBUG)
Background job completed #13 (INFO)
System health check #14 (ERROR)
Email notification sent #15 (INFO)
System health check #16 (WARN)
Memory usage checked #17 (ERROR)
Background job completed #18 (ERROR)
Memory usage checked #19 (DEBUG)
Processing user request #20 (DEBUG)
File uploaded #21 (ERROR)
Database query executed #22 (WARN)
Database query executed #23 (DEBUG)
Database query executed #24 (ERROR)
Database query executed #25 (WARN)
Memory usage checked #26 (INFO)
Disk space monitored #27 (ERROR)
API response sent #28 (DEBUG)
Email notification sent #29 (WARN)
Processing user request #30 (INFO)
System health check #31 (ERROR)
Disk space monitored #32 (DEBUG)
Memory usage checked #33 (WARN)
Database query executed #34 (INFO)
File uploaded #35 (DEBUG)
Background job completed #36 (INFO)
File uploaded #37 (WARN)
System health check #38 (ERROR)
Cache updated #39 (DEBUG)
Email notification sent #40 (DEBUG)
Memory usage checked #41 (WARN)
Database query executed #42 (ERROR)
Email notification sent #43 (DEBUG)
Background job completed #44 (WARN)
System health check #45 (INFO)
File uploaded #46 (INFO)
File uploaded #47 (ERROR)
File uploaded #48 (INFO)
Memory usage checked #49 (ERROR)
Email notification sent #50 (DEBUG)
Cache updated #51 (WARN)
API response sent #52 (WARN)
Memory usage checked #53 (WARN)
Processing user request #54 (WARN)
File uploaded #55 (INFO)
API response sent #56 (WARN)
Disk space monitored #57 (INFO)
Background job completed #58 (DEBUG)
Email notification sent #59 (WARN)
Memory usage checked #60 (INFO)
File uploaded #61 (ERROR)
Cache updated #62 (WARN)
Memory usage checked #63 (DEBUG)
Cache updated #64 (INFO)
System health check #65 (ERROR)
Memory usage checked #66 (INFO)
Disk space monitored #67 (ERROR)
API response sent #68 (INFO)
Background job completed #69 (ERROR)
System health check #70 (DEBUG)
System health check #71 (WARN)
Email notification sent #72 (INFO)
Email notification sent #73 (ERROR)
Background job completed #74 (ERROR)
Processing user request #75 (DEBUG)
Email notification sent #76 (ERROR)
Disk space monitored #77 (WARN)
Background job completed #78 (ERROR)
Processing user request #79 (INFO)
API response sent #80 (ERROR)
API response sent #81 (INFO)
File uploaded #82 (DEBUG)
File uploaded #83 (ERROR)
Database query executed #84 (WARN)
File uploaded #85 (INFO)
Cache updated #86 (INFO)
API response sent #87 (ERROR)
Database query executed #88 (WARN)
Cache updated #89 (ERROR)
Email notification sent #90 (ERROR)
System health check #91 (ERROR)
System health check #92 (DEBUG)
Email notification sent #93 (DEBUG)
API response sent #94 (INFO)
API response sent #95 (WARN)
Processing user request #96 (WARN)
Background job completed #97 (ERROR)
Memory usage checked #98 (DEBUG)
System health check #99 (WARN)
Database query executed #100 (ERROR)
System health check #101 (DEBUG)
Disk space monitored #102 (DEBUG)
Background job completed #103 (INFO)
Disk space monitored #104 (DEBUG)
Processing user request #105 (WARN)
Cache updated #106 (DEBUG)
System health check #107 (DEBUG)
Processing user request #108 (DEBUG)
Cache updated #109 (ERROR)
Cache updated #110 (ERROR)
Processing user request #111 (INFO)
API response sent #112 (INFO)
Cache updated #113 (WARN)
Foo was here
Bar was here
