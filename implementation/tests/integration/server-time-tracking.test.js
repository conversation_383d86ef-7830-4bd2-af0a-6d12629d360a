/**
 * Integration tests for server-side stateful time tracking
 * Tests the actual server implementation with time tracking features
 */

import { describe, test, expect, beforeEach, afterEach, beforeAll, afterAll } from '@jest/globals';
import request from 'supertest';
import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

// Import the actual server app
import app from '../../server.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

let testFilePath;

describe('Server Time Tracking Integration', () => {
  beforeEach(async () => {
    // Create a test file for each test
    testFilePath = await global.testUtils.createTestFile('time-tracking-integration.log', 
      'Initial line 1\nInitial line 2\nInitial line 3\n');
  });

  afterEach(async () => {
    // Stop any active tailing to clean up
    try {
      await request(app)
        .post('/api/tail/stop')
        .send({ filePath: testFilePath });
    } catch (error) {
      // Ignore cleanup errors
    }
    
    // Clean up test files
    await global.testUtils.cleanupTestFiles();
  });

  describe('Timer Management', () => {
    test('should start and stop internal timers correctly', async () => {
      // Check initial health status
      let healthResponse = await request(app)
        .get('/health')
        .expect(200);

      const initialTimers = healthResponse.body.activeTimers;
      const initialMetadata = healthResponse.body.activeMetadata;

      // Start tailing
      const startResponse = await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      expect(startResponse.body.success).toBe(true);
      expect(startResponse.body.filePath).toBe(testFilePath);
      expect(startResponse.body.initialContent).toContain('Initial line 1');

      // Check that timer and metadata were created
      healthResponse = await request(app)
        .get('/health')
        .expect(200);

      expect(healthResponse.body.activeTimers).toBe(initialTimers + 1);
      expect(healthResponse.body.activeMetadata).toBe(initialMetadata + 1);

      // Stop tailing
      const stopResponse = await request(app)
        .post('/api/tail/stop')
        .send({ filePath: testFilePath })
        .expect(200);

      expect(stopResponse.body.success).toBe(true);

      // Check that timer and metadata were cleaned up
      healthResponse = await request(app)
        .get('/health')
        .expect(200);

      expect(healthResponse.body.activeTimers).toBe(initialTimers);
      expect(healthResponse.body.activeMetadata).toBe(initialMetadata);
    });

    test('should handle multiple files with independent timers', async () => {
      const testFilePath2 = await global.testUtils.createTestFile('time-tracking-integration2.log', 
        'File 2 Line 1\nFile 2 Line 2\n');

      try {
        // Start tailing both files
        await request(app)
          .post('/api/tail/start')
          .send({ filePath: testFilePath })
          .expect(200);

        await request(app)
          .post('/api/tail/start')
          .send({ filePath: testFilePath2 })
          .expect(200);

        // Check that both timers are active
        let healthResponse = await request(app)
          .get('/health')
          .expect(200);

        expect(healthResponse.body.activeTimers).toBeGreaterThanOrEqual(2);
        expect(healthResponse.body.activeMetadata).toBeGreaterThanOrEqual(2);

        // Stop one file
        await request(app)
          .post('/api/tail/stop')
          .send({ filePath: testFilePath })
          .expect(200);

        // Check that one timer is still active
        healthResponse = await request(app)
          .get('/health')
          .expect(200);

        expect(healthResponse.body.activeTimers).toBeGreaterThanOrEqual(1);
        expect(healthResponse.body.activeMetadata).toBeGreaterThanOrEqual(1);

        // Stop second file
        await request(app)
          .post('/api/tail/stop')
          .send({ filePath: testFilePath2 })
          .expect(200);

      } finally {
        // Cleanup
        try {
          await request(app).post('/api/tail/stop').send({ filePath: testFilePath2 });
        } catch (error) {
          // Ignore cleanup errors
        }
      }
    });

    test('should replace timer when restarting tail on same file', async () => {
      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Check timer count
      let healthResponse = await request(app)
        .get('/health')
        .expect(200);

      const timerCount = healthResponse.body.activeTimers;

      // Wait a bit
      await global.testUtils.wait(100);

      // Start tailing again (should replace existing timer)
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Check that timer count is the same (replaced, not added)
      healthResponse = await request(app)
        .get('/health')
        .expect(200);

      expect(healthResponse.body.activeTimers).toBe(timerCount);
      expect(healthResponse.body.activeMetadata).toBe(1);
    });
  });

  describe('Timestamp Metadata', () => {
    test('should not expose timestamp metadata in API responses', async () => {
      // Start tailing
      const startResponse = await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Check that start response doesn't include metadata
      expect(startResponse.body.metadata).toBeUndefined();
      expect(startResponse.body.startTime).toBeUndefined();
      expect(startResponse.body.lines).toBeUndefined();
      expect(startResponse.body.timestamp).toBeUndefined();

      // Check updates response doesn't include metadata
      const updatesResponse = await request(app)
        .get(`/api/tail/updates/${encodeURIComponent(testFilePath)}`)
        .expect(200);

      expect(updatesResponse.body.metadata).toBeUndefined();
      expect(updatesResponse.body.startTime).toBeUndefined();
      expect(updatesResponse.body.lines).toBeUndefined();
      expect(updatesResponse.body.timestamp).toBeUndefined();
    });

    test('should not alter source file on disk', async () => {
      // Read original file content
      const originalContent = await fs.readFile(testFilePath, 'utf8');

      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Wait a bit for any potential file modifications
      await global.testUtils.wait(200);

      // Verify source file is unchanged
      const currentContent = await fs.readFile(testFilePath, 'utf8');
      expect(currentContent).toBe(originalContent);

      // Stop tailing
      await request(app)
        .post('/api/tail/stop')
        .send({ filePath: testFilePath })
        .expect(200);

      // Verify source file is still unchanged
      const finalContent = await fs.readFile(testFilePath, 'utf8');
      expect(finalContent).toBe(originalContent);
    });
  });

  describe('File Updates with Time Tracking', () => {
    test('should handle file updates while maintaining time tracking', async () => {
      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Add new content to the file
      await fs.appendFile(testFilePath, 'New line 4\nNew line 5\n');

      // Wait for the update to be detected
      await global.testUtils.wait(100);

      // Get updates
      const updatesResponse = await request(app)
        .get(`/api/tail/updates/${encodeURIComponent(testFilePath)}`)
        .expect(200);

      expect(updatesResponse.body.newContent).toContain('New line 4');
      expect(updatesResponse.body.newContent).toContain('New line 5');
      
      // Verify metadata is not exposed
      expect(updatesResponse.body.metadata).toBeUndefined();
      expect(updatesResponse.body.timestamp).toBeUndefined();
    });
  });

  describe('Relative Time Seeking', () => {
    test('should perform relative time seeking via API', async () => {
      // Create a file with timestamped content
      const now = new Date();
      const content = `${now.toISOString()} Recent log entry\n${new Date(now.getTime() - 30000).toISOString()} Older log entry\n`;
      await fs.writeFile(testFilePath, content);

      const response = await request(app)
        .post('/api/tail/seek')
        .send({
          filePath: testFilePath,
          timeExpression: 'last 1 minute'
        })
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.content).toBeDefined();
      expect(response.body.seekTime).toBeDefined();
      expect(response.body.timeRange).toBeDefined();
      expect(response.body.timeRange.type).toBe('range');
    });

    test('should handle seek with missing parameters', async () => {
      // Missing filePath
      await request(app)
        .post('/api/tail/seek')
        .send({ timeExpression: 'last 1 minute' })
        .expect(400);

      // Missing timeExpression
      await request(app)
        .post('/api/tail/seek')
        .send({ filePath: testFilePath })
        .expect(400);
    });

    test('should handle seek with non-existent file', async () => {
      const response = await request(app)
        .post('/api/tail/seek')
        .send({
          filePath: 'non-existent-file.log',
          timeExpression: 'last 1 minute'
        })
        .expect(500);

      expect(response.body.error).toBeDefined();
    });
  });

  describe('Error Handling', () => {
    test('should handle stopping non-existent tailer gracefully', async () => {
      const nonExistentPath = 'non-existent-file.log';

      const response = await request(app)
        .post('/api/tail/stop')
        .send({ filePath: nonExistentPath })
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    test('should handle file deletion during tailing', async () => {
      // Start tailing
      await request(app)
        .post('/api/tail/start')
        .send({ filePath: testFilePath })
        .expect(200);

      // Delete the file
      await fs.unlink(testFilePath);

      // Try to get updates (should handle gracefully)
      const updatesResponse = await request(app)
        .get(`/api/tail/updates/${encodeURIComponent(testFilePath)}`);

      // Should either return an error or handle gracefully
      expect([200, 404, 500]).toContain(updatesResponse.status);
    });
  });
});
